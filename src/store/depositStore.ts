import { makeAutoObservable } from "mobx";
import { Network } from "@/types/networks.ts";
import { networks } from "@/config/network.ts";

export class DepositStore {
  fromNetwork: Network;
  toNetwork: Network;

  constructor() {
    this.fromNetwork = networks[0] || null;
    this.toNetwork = networks[1] || null;

    makeAutoObservable(this);
  }

  setFromNetwork(network: Network) {
    this.fromNetwork = network;
  }

  setToNetwork(network: Network) {
    this.toNetwork = network;
  }

  swapNetworks() {
    const temp = this.fromNetwork;
    this.fromNetwork = this.toNetwork;
    this.toNetwork = temp;
  }
}
